package com.android.rockchip.camera2.integrated.browser;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.view.TextureView;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.GestureDetector;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.touptek.ui.TpImageView;
import com.touptek.video.TpImageLoader;
import com.touptek.video.TpVideoSystem;
import com.touptek.video.TpVideoConfig;
import com.android.rockchip.mediacodecnew.R;

// Matrix和RectF不再需要，使用View缩放方案

import java.io.File;

/**
 * ImageVideoCompareActivity - 图片视频对比界面
 *
 * 提供左右分屏对比功能：
 * - 左侧：显示用户选择的图片
 * - 右侧：显示实时相机预览
 */
public class ImageVideoCompareActivity extends AppCompatActivity {
    private static final String TAG = "ImageVideoCompare";
    private static final int REQUEST_CAMERA_PERMISSION = 200;

    // UI组件
    private TpImageView imageView;
    private TextureView textureView;
    private TextView tvImageInfo;
    private TextView tvPreviewInfo;
    private Button btnBack;

    // 视频系统（简化模式）
    private TpVideoSystem videoSystem;

    // 图片路径
    private String imagePath;

    // Camera实际输出尺寸缓存
    private android.util.Size cameraActualOutputSize;

    // 手势检测器
    private ScaleGestureDetector scaleGestureDetector;
    private GestureDetector gestureDetector;

    // 缩放和平移状态
    private float currentScale = 1.0f;
    private float baseScaleX = 1.0f;
    private float baseScaleY = 1.0f;
    private float translateX = 0f;
    private float translateY = 0f;

    // 缩放限制
    private static final float MIN_SCALE = 0.5f;
    private static final float MAX_SCALE = 3.0f;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_image_video_compare);

        // 获取传入的图片路径
        imagePath = getIntent().getStringExtra("image_path");
        if (imagePath == null) {
            Log.e(TAG, "未接收到图片路径");
            Toast.makeText(this, "图片路径错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 初始化UI组件
        initViews();

        // 加载图片
        loadImage();

        // 检查相机权限并启动预览
        checkCameraPermissionAndStart();
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        imageView = findViewById(R.id.image_view);
        textureView = findViewById(R.id.texture_view);
        tvImageInfo = findViewById(R.id.tv_image_info);
        tvPreviewInfo = findViewById(R.id.tv_preview_info);
        btnBack = findViewById(R.id.btn_back);

        // 设置返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 初始化手势检测器
        setupGestureDetectors();

        Log.d(TAG, "UI组件初始化完成");
    }

    /**
     * 初始化手势检测器
     */
    private void setupGestureDetectors() {
        // 缩放手势检测器
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                float scaleFactor = detector.getScaleFactor();
                float newScale = currentScale * scaleFactor;

                // 限制缩放范围
                newScale = Math.max(MIN_SCALE, Math.min(newScale, MAX_SCALE));

                if (newScale != currentScale) {
                    currentScale = newScale;
                    applyTransformations();
                    Log.d(TAG, "缩放: " + currentScale);
                }
                return true;
            }
        });

        // 平移手势检测器
        gestureDetector = new GestureDetector(this, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                // 限制平移范围，避免无限累积
                float maxTranslate = 1000f; // 最大平移距离

                translateX = Math.max(-maxTranslate, Math.min(maxTranslate, translateX - distanceX));
                translateY = Math.max(-maxTranslate, Math.min(maxTranslate, translateY - distanceY));

                applyTransformations();
                Log.d(TAG, "平移: (" + String.format("%.1f", translateX) + ", " + String.format("%.1f", translateY) + ")");
                return true;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                // 双击重置到初始状态
                resetTransformations();
                Log.d(TAG, "双击重置");
                return true;
            }
        });

        // 设置TextureView的触摸监听器
        textureView.setOnTouchListener((v, event) -> {
            // 优先处理缩放手势
            boolean scaleHandled = scaleGestureDetector.onTouchEvent(event);

            // 只有在非缩放状态下才处理平移
            boolean gestureHandled = false;
            if (!scaleGestureDetector.isInProgress()) {
                gestureHandled = gestureDetector.onTouchEvent(event);
            }

            return scaleHandled || gestureHandled;
        });
    }





    /**
     * 加载选中的图片
     */
    private void loadImage() {
        try {
            // 使用TpImageLoader加载图片
            TpImageLoader.loadFullImage(imagePath, imageView);

            // 显示图片信息
            File imageFile = new File(imagePath);
            String fileName = imageFile.getName();
            tvImageInfo.setText(fileName);

            Log.d(TAG, "图片加载完成: " + fileName);

        } catch (Exception e) {
            Log.e(TAG, "加载图片失败", e);
            Toast.makeText(this, "加载图片失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 检查相机权限并启动预览
     */
    private void checkCameraPermissionAndStart() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        } else {
            requestCameraPermission();
        }
    }

    /**
     * 请求相机权限
     */
    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA},
                REQUEST_CAMERA_PERMISSION);
    }

    /**
     * 启动相机预览（使用TpVideoSystem）
     */
    private void startCameraPreview() {
        try {
            // 创建简化的视频配置
            TpVideoConfig config = TpVideoConfig.createDefault4K();
            videoSystem = new TpVideoSystem(this, config);

            // 设置监听器
            videoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
                @Override
                public void onCameraStarted() {
                    Log.d(TAG, "对比模式相机启动完成");
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("实时预览");
                        // 应用CENTER_CROP Matrix变换
                        configureTransform();

                        // 延迟验证和重新应用变换（确保Camera完全稳定）
                        textureView.postDelayed(() -> {
                            Log.d(TAG, "执行延迟变换验证");
                            configureTransform();
                        }, 1000);
                    });
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "对比模式相机错误: " + errorMessage);
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("预览错误");
                        Toast.makeText(ImageVideoCompareActivity.this,
                            "预览错误: " + errorMessage, Toast.LENGTH_SHORT).show();
                    });
                }
            });

            // 等待TextureView准备好后初始化
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            } else {
                textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
                    @Override
                    public void onSurfaceTextureAvailable(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture可用，TextureView尺寸: " + width + "x" + height);
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public void onSurfaceTextureSizeChanged(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture尺寸改变: " + width + "x" + height);
                        // 重新设置buffer size和变换
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public boolean onSurfaceTextureDestroyed(@NonNull android.graphics.SurfaceTexture surface) {
                        return true;
                    }

                    @Override
                    public void onSurfaceTextureUpdated(@NonNull android.graphics.SurfaceTexture surface) {}
                });
            }

            Log.d(TAG, "TpVideoSystem预览已启动");

        } catch (Exception e) {
            Log.e(TAG, "启动TpVideoSystem预览失败", e);
            Toast.makeText(this, "启动预览失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 设置SurfaceTexture并初始化视频系统
     * 核心解决方案：设置SurfaceTexture buffer size为Camera输出尺寸，避免变形
     */
    private void setupSurfaceTextureAndInit() {
        if (textureView == null || textureView.getSurfaceTexture() == null) {
            Log.w(TAG, "TextureView或SurfaceTexture未准备好");
            return;
        }

        try {
            // 1. 获取Camera实际输出尺寸
            android.util.Size cameraOutputSize = getCameraActualOutputSize();
            if (cameraOutputSize == null) {
                Log.w(TAG, "无法获取Camera输出尺寸，使用默认初始化");
                initVideoSystem();
                return;
            }

            // 2. 设置SurfaceTexture buffer size为Camera输出尺寸（关键步骤）
            android.graphics.SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
            surfaceTexture.setDefaultBufferSize(cameraOutputSize.getWidth(), cameraOutputSize.getHeight());

            Log.d(TAG, "✅ SurfaceTexture buffer size已设置为: " + cameraOutputSize.getWidth() + "x" + cameraOutputSize.getHeight());
            Log.d(TAG, "📐 这将避免Camera输出在SurfaceTexture层面的变形");

            // 3. 初始化视频系统
            initVideoSystem();

        } catch (Exception e) {
            Log.e(TAG, "设置SurfaceTexture失败，使用默认初始化", e);
            initVideoSystem();
        }
    }

    /**
     * 初始化视频系统
     */
    private void initVideoSystem() {
        if (videoSystem != null && textureView.getSurfaceTexture() != null) {
            android.view.Surface surface = new android.view.Surface(textureView.getSurfaceTexture());
            videoSystem.initialize(surface);
        }
    }

    /**
     * 获取Camera实际输出尺寸
     * 这是解决变形问题的关键：获取真实的Camera stream输出尺寸
     */
    private android.util.Size getCameraActualOutputSize() {
        if (cameraActualOutputSize != null) {
            return cameraActualOutputSize;
        }

        try {
            if (videoSystem != null && videoSystem.getVideoConfig() != null) {
                // 获取配置的视频尺寸作为基础
                android.util.Size configSize = videoSystem.getVideoConfig().getSize();

                // 缓存结果
                cameraActualOutputSize = configSize;

                Log.d(TAG, "Camera实际输出尺寸: " + configSize.getWidth() + "x" + configSize.getHeight());
                return configSize;
            }
        } catch (Exception e) {
            Log.e(TAG, "获取Camera输出尺寸失败", e);
        }

        // 如果无法获取，返回4K默认尺寸
        cameraActualOutputSize = new android.util.Size(3840, 2160);
        Log.w(TAG, "使用默认Camera输出尺寸: 3840x2160");
        return cameraActualOutputSize;
    }

    /**
     * 配置TextureView的缩放，实现CENTER_CROP效果
     * 方案1：使用View缩放属性，避开复杂的Matrix变换
     */
    private void configureTransform() {
        if (textureView == null) {
            return;
        }

        textureView.post(() -> {
            try {
                // 获取TextureView的尺寸
                int viewWidth = textureView.getWidth();
                int viewHeight = textureView.getHeight();

                if (viewWidth <= 0 || viewHeight <= 0) {
                    Log.w(TAG, "TextureView尺寸无效: " + viewWidth + "x" + viewHeight);
                    return;
                }

                // 获取Camera实际输出尺寸
                android.util.Size cameraSize = getCameraActualOutputSize();
                int cameraWidth = cameraSize.getWidth();
                int cameraHeight = cameraSize.getHeight();

                Log.d(TAG, "配置View缩放 CENTER_CROP:");
                Log.d(TAG, "  TextureView尺寸: " + viewWidth + "x" + viewHeight);
                Log.d(TAG, "  Camera输出尺寸: " + cameraWidth + "x" + cameraHeight);

                // 计算宽高比
                float cameraRatio = (float) cameraWidth / cameraHeight;   // 16:9 ≈ 1.78
                float viewRatio = (float) viewWidth / viewHeight;         // 接近 1:1

                Log.d(TAG, "  Camera宽高比: " + String.format("%.3f", cameraRatio));
                Log.d(TAG, "  TextureView宽高比: " + String.format("%.3f", viewRatio));

                // 设置缩放中心为TextureView的中心
                textureView.setPivotX(viewWidth / 2f);
                textureView.setPivotY(viewHeight / 2f);

                // FIT_CENTER逻辑：计算基础缩放比例（与TpImageView一致）
                // 使用Math.min确保完整显示，不裁剪任何内容
                float scaleX = (float) viewWidth / cameraWidth;
                float scaleY = (float) viewHeight / cameraHeight;
                float fitCenterScale = Math.min(scaleX, scaleY); // 取较小值，确保完整显示

                baseScaleX = fitCenterScale;
                baseScaleY = fitCenterScale;

                Log.d(TAG, "  FIT_CENTER模式:");
                Log.d(TAG, "    scaleX候选: " + scaleX + ", scaleY候选: " + scaleY);
                Log.d(TAG, "    选择较小值: " + fitCenterScale + " (确保完整显示)");
                Log.d(TAG, "    效果: 视频完整可见，可能有黑边");

                // 重置手势状态
                currentScale = 1.0f;
                translateX = 0f;
                translateY = 0f;

                // 应用初始变换
                applyTransformations();

                Log.d(TAG, "✅ FIT_CENTER变换已应用:");
                Log.d(TAG, "  统一缩放: " + baseScaleX + " (X和Y轴相同)");
                Log.d(TAG, "  缩放中心: (" + (viewWidth/2f) + ", " + (viewHeight/2f) + ")");
                Log.d(TAG, "📐 效果: 保持" + String.format("%.2f", cameraRatio) + ":1宽高比，完整显示，支持手势操作");
                Log.d(TAG, "🎯 与左侧图片显示模式一致: FIT_CENTER (无裁剪)");

            } catch (Exception e) {
                Log.e(TAG, "配置TextureView缩放失败", e);
            }
        });
    }
    /**
     * 应用所有变换（基础缩放 + 手势缩放 + 平移）
     */
    private void applyTransformations() {
        if (textureView == null) return;

        // 计算最终的缩放值
        float finalScaleX = baseScaleX * currentScale;
        float finalScaleY = baseScaleY * currentScale;

        // 智能边界检查：根据缩放程度调整平移范围
        float viewWidth = textureView.getWidth();
        float viewHeight = textureView.getHeight();

        if (viewWidth > 0 && viewHeight > 0) {
            // 计算合理的平移范围（基于视图尺寸和缩放比例）
            float maxTranslateX = viewWidth * 0.5f * Math.max(0, finalScaleX - 1.0f);
            float maxTranslateY = viewHeight * 0.5f * Math.max(0, finalScaleY - 1.0f);

            // 限制平移范围
            translateX = Math.max(-maxTranslateX, Math.min(maxTranslateX, translateX));
            translateY = Math.max(-maxTranslateY, Math.min(maxTranslateY, translateY));
        }

        // 应用变换
        textureView.setScaleX(finalScaleX);
        textureView.setScaleY(finalScaleY);
        textureView.setTranslationX(translateX);
        textureView.setTranslationY(translateY);

        // 测试旋转修正 - 解决视频竖屏显示问题
        textureView.setRotation(90f); // 先试试顺时针90度

        Log.d(TAG, "应用旋转: 90度 (如果不对，可以试试270度或180度)");
    }

    /**
     * 重置所有变换到初始状态
     */
    private void resetTransformations() {
        currentScale = 1.0f;
        translateX = 0f;
        translateY = 0f;
        applyTransformations();
        Log.d(TAG, "变换已重置到初始状态");
    }

    /**
     * 测试不同的旋转角度（用于调试）
     * 可以在日志中调用这个方法来测试不同角度
     */
    private void testRotation(float degrees) {
        if (textureView != null) {
            textureView.setRotation(degrees);
            Log.d(TAG, "🔄 测试旋转角度: " + degrees + "度");
        }
    }






    /**
     * 停止相机预览
     */
    private void stopCameraPreview() {
        if (videoSystem != null) {
            videoSystem.release();
            videoSystem = null;
            Log.d(TAG, "TpVideoSystem预览已停止");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startCameraPreview();
            } else {
                Toast.makeText(this, "需要相机权限才能显示实时预览", Toast.LENGTH_LONG).show();
                tvPreviewInfo.setText("无相机权限");
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 如果视频系统已经初始化，重新启动
        if (videoSystem != null && ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            }
        } else if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        stopCameraPreview();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopCameraPreview();
        Log.d(TAG, "Activity已销毁，资源已清理");
    }
}
