package com.android.rockchip.camera2.integrated.browser;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.view.TextureView;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.touptek.ui.TpImageView;
import com.touptek.video.TpImageLoader;
import com.touptek.video.TpVideoSystem;
import com.touptek.video.TpVideoConfig;
import com.android.rockchip.mediacodecnew.R;

import android.graphics.Matrix;
import android.graphics.RectF;

import java.io.File;

/**
 * ImageVideoCompareActivity - 图片视频对比界面
 * 
 * 提供左右分屏对比功能：
 * - 左侧：显示用户选择的图片
 * - 右侧：显示实时相机预览
 */
public class ImageVideoCompareActivity extends AppCompatActivity {
    private static final String TAG = "ImageVideoCompare";
    private static final int REQUEST_CAMERA_PERMISSION = 200;
    
    // UI组件
    private TpImageView imageView;
    private TextureView textureView;
    private TextView tvImageInfo;
    private TextView tvPreviewInfo;
    private Button btnBack;
    
    // 视频系统（简化模式）
    private TpVideoSystem videoSystem;

    // 图片路径
    private String imagePath;

    // Camera实际输出尺寸缓存
    private android.util.Size cameraActualOutputSize;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_image_video_compare);
        
        // 获取传入的图片路径
        imagePath = getIntent().getStringExtra("image_path");
        if (imagePath == null) {
            Log.e(TAG, "未接收到图片路径");
            Toast.makeText(this, "图片路径错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        // 初始化UI组件
        initViews();
        
        // 加载图片
        loadImage();
        
        // 检查相机权限并启动预览
        checkCameraPermissionAndStart();
    }
    
    /**
     * 初始化UI组件
     */
    private void initViews() {
        imageView = findViewById(R.id.image_view);
        textureView = findViewById(R.id.texture_view);
        tvImageInfo = findViewById(R.id.tv_image_info);
        tvPreviewInfo = findViewById(R.id.tv_preview_info);
        btnBack = findViewById(R.id.btn_back);
        
        // 设置返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 注意：使用普通TextureView测试Matrix变换方案

        Log.d(TAG, "UI组件初始化完成");
    }
    




    /**
     * 加载选中的图片
     */
    private void loadImage() {
        try {
            // 使用TpImageLoader加载图片
            TpImageLoader.loadFullImage(imagePath, imageView);
            
            // 显示图片信息
            File imageFile = new File(imagePath);
            String fileName = imageFile.getName();
            tvImageInfo.setText(fileName);
            
            Log.d(TAG, "图片加载完成: " + fileName);
            
        } catch (Exception e) {
            Log.e(TAG, "加载图片失败", e);
            Toast.makeText(this, "加载图片失败", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 检查相机权限并启动预览
     */
    private void checkCameraPermissionAndStart() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        } else {
            requestCameraPermission();
        }
    }
    
    /**
     * 请求相机权限
     */
    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.CAMERA}, 
                REQUEST_CAMERA_PERMISSION);
    }
    
    /**
     * 启动相机预览（使用TpVideoSystem）
     */
    private void startCameraPreview() {
        try {
            // 创建简化的视频配置
            TpVideoConfig config = TpVideoConfig.createDefault4K();
            videoSystem = new TpVideoSystem(this, config);

            // 设置监听器
            videoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
                @Override
                public void onCameraStarted() {
                    Log.d(TAG, "对比模式相机启动完成");
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("实时预览");
                        // 应用CENTER_CROP Matrix变换
                        configureTransform();

                        // 延迟验证和重新应用变换（确保Camera完全稳定）
                        textureView.postDelayed(() -> {
                            Log.d(TAG, "执行延迟变换验证");
                            configureTransform();
                        }, 1000);
                    });
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "对比模式相机错误: " + errorMessage);
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("预览错误");
                        Toast.makeText(ImageVideoCompareActivity.this,
                            "预览错误: " + errorMessage, Toast.LENGTH_SHORT).show();
                    });
                }
            });

            // 等待TextureView准备好后初始化
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            } else {
                textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
                    @Override
                    public void onSurfaceTextureAvailable(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture可用，TextureView尺寸: " + width + "x" + height);
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public void onSurfaceTextureSizeChanged(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture尺寸改变: " + width + "x" + height);
                        // 重新设置buffer size和变换
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public boolean onSurfaceTextureDestroyed(@NonNull android.graphics.SurfaceTexture surface) {
                        return true;
                    }

                    @Override
                    public void onSurfaceTextureUpdated(@NonNull android.graphics.SurfaceTexture surface) {}
                });
            }

            Log.d(TAG, "TpVideoSystem预览已启动");

        } catch (Exception e) {
            Log.e(TAG, "启动TpVideoSystem预览失败", e);
            Toast.makeText(this, "启动预览失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 设置SurfaceTexture并初始化视频系统
     * 核心解决方案：设置SurfaceTexture buffer size为Camera输出尺寸，避免变形
     */
    private void setupSurfaceTextureAndInit() {
        if (textureView == null || textureView.getSurfaceTexture() == null) {
            Log.w(TAG, "TextureView或SurfaceTexture未准备好");
            return;
        }

        try {
            // 1. 获取Camera实际输出尺寸
            android.util.Size cameraOutputSize = getCameraActualOutputSize();
            if (cameraOutputSize == null) {
                Log.w(TAG, "无法获取Camera输出尺寸，使用默认初始化");
                initVideoSystem();
                return;
            }

            // 2. 设置SurfaceTexture buffer size为Camera输出尺寸（关键步骤）
            android.graphics.SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
            surfaceTexture.setDefaultBufferSize(cameraOutputSize.getWidth(), cameraOutputSize.getHeight());

            Log.d(TAG, "✅ SurfaceTexture buffer size已设置为: " + cameraOutputSize.getWidth() + "x" + cameraOutputSize.getHeight());
            Log.d(TAG, "📐 这将避免Camera输出在SurfaceTexture层面的变形");

            // 3. 初始化视频系统
            initVideoSystem();

        } catch (Exception e) {
            Log.e(TAG, "设置SurfaceTexture失败，使用默认初始化", e);
            initVideoSystem();
        }
    }

    /**
     * 初始化视频系统
     */
    private void initVideoSystem() {
        if (videoSystem != null && textureView.getSurfaceTexture() != null) {
            android.view.Surface surface = new android.view.Surface(textureView.getSurfaceTexture());
            videoSystem.initialize(surface);
        }
    }

    /**
     * 获取Camera实际输出尺寸
     * 这是解决变形问题的关键：获取真实的Camera stream输出尺寸
     */
    private android.util.Size getCameraActualOutputSize() {
        if (cameraActualOutputSize != null) {
            return cameraActualOutputSize;
        }

        try {
            if (videoSystem != null && videoSystem.getVideoConfig() != null) {
                // 获取配置的视频尺寸作为基础
                android.util.Size configSize = videoSystem.getVideoConfig().getSize();

                // 缓存结果
                cameraActualOutputSize = configSize;

                Log.d(TAG, "Camera实际输出尺寸: " + configSize.getWidth() + "x" + configSize.getHeight());
                return configSize;
            }
        } catch (Exception e) {
            Log.e(TAG, "获取Camera输出尺寸失败", e);
        }

        // 如果无法获取，返回4K默认尺寸
        cameraActualOutputSize = new android.util.Size(3840, 2160);
        Log.w(TAG, "使用默认Camera输出尺寸: 3840x2160");
        return cameraActualOutputSize;
    }

    /**
     * 配置TextureView的变换矩阵，实现CENTER_CROP效果
     * 基于SurfaceTexture buffer size方案的Matrix变换
     */
    private void configureTransform() {
        if (textureView == null) {
            return;
        }

        textureView.post(() -> {
            try {
                // 获取TextureView的尺寸
                int viewWidth = textureView.getWidth();
                int viewHeight = textureView.getHeight();

                if (viewWidth <= 0 || viewHeight <= 0) {
                    Log.w(TAG, "TextureView尺寸无效: " + viewWidth + "x" + viewHeight);
                    return;
                }

                // 获取Camera实际输出尺寸
                android.util.Size cameraSize = getCameraActualOutputSize();
                int cameraWidth = cameraSize.getWidth();
                int cameraHeight = cameraSize.getHeight();

                Log.d(TAG, "配置CENTER_CROP变换:");
                Log.d(TAG, "  TextureView尺寸: " + viewWidth + "x" + viewHeight);
                Log.d(TAG, "  Camera输出尺寸: " + cameraWidth + "x" + cameraHeight);

                // 计算CENTER_CROP缩放比例（取较大值，填满容器）
                float scaleX = (float) viewWidth / cameraWidth;
                float scaleY = (float) viewHeight / cameraHeight;
                float scale = Math.max(scaleX, scaleY);

                Log.d(TAG, "  缩放比例: scaleX=" + scaleX + ", scaleY=" + scaleY + ", 选择=" + scale);

                // 创建变换矩阵
                Matrix matrix = new Matrix();

                // 应用缩放
                matrix.postScale(scale, scale);

                // 计算居中偏移
                float dx = (viewWidth - cameraWidth * scale) / 2f;
                float dy = (viewHeight - cameraHeight * scale) / 2f;
                matrix.postTranslate(dx, dy);

                // 应用变换矩阵
                textureView.setTransform(matrix);

                Log.d(TAG, "✅ CENTER_CROP变换已应用: scale=" + scale + ", dx=" + dx + ", dy=" + dy);
                Log.d(TAG, "📐 效果: 保持" + (cameraWidth/(float)cameraHeight) + ":1宽高比，填满容器");

            } catch (Exception e) {
                Log.e(TAG, "配置TextureView变换失败", e);
            }
        });
    }



    /**
     * 设置CENTER_CROP模式的Transform，保持视频宽高比不变形
     * 类似ImageView的CENTER_CROP模式：取较大的缩放比例，裁剪多余部分
     */
    private void setupCenterCropTransform() {
        if (textureView == null || videoSystem == null) {
            return;
        }

        textureView.post(() -> {
            try {
                int viewWidth = textureView.getWidth();
                int viewHeight = textureView.getHeight();

                if (viewWidth <= 0 || viewHeight <= 0) {
                    Log.w(TAG, "TextureView尺寸无效: " + viewWidth + "x" + viewHeight);
                    return;
                }

                // 获取视频分辨率
                android.util.Size videoSize = videoSystem.getVideoConfig().getSize();
                int videoWidth = videoSize.getWidth();
                int videoHeight = videoSize.getHeight();

                if (videoWidth <= 0 || videoHeight <= 0) {
                    Log.w(TAG, "视频尺寸无效: " + videoWidth + "x" + videoHeight);
                    return;
                }

                Log.d(TAG, "设置CENTER_CROP变换:");
                Log.d(TAG, "  视图尺寸: " + viewWidth + "x" + viewHeight);
                Log.d(TAG, "  视频尺寸: " + videoWidth + "x" + videoHeight);

                // CENTER_CROP模式：取较大的缩放比例，确保填满容器
                float scaleX = (float) viewWidth / videoWidth;
                float scaleY = (float) viewHeight / videoHeight;
                float scale = Math.max(scaleX, scaleY); // 取较大值，类似CENTER_CROP

                Log.d(TAG, "  缩放比例: scaleX=" + scaleX + ", scaleY=" + scaleY + ", 选择=" + scale);

                // 创建变换矩阵
                Matrix matrix = new Matrix();

                // 应用缩放
                matrix.postScale(scale, scale);

                // 居中显示
                float dx = (viewWidth - videoWidth * scale) / 2f;
                float dy = (viewHeight - videoHeight * scale) / 2f;
                matrix.postTranslate(dx, dy);

                // 应用变换矩阵
                textureView.setTransform(matrix);

                Log.d(TAG, "✅ CENTER_CROP变换已应用: scale=" + scale + ", dx=" + dx + ", dy=" + dy);
                Log.d(TAG, "📐 效果: 视频保持" + (videoWidth/(float)videoHeight) + ":1宽高比，填满容器");

            } catch (Exception e) {
                Log.e(TAG, "设置CENTER_CROP变换失败", e);
            }
        });
    }


    
    /**
     * 停止相机预览
     */
    private void stopCameraPreview() {
        if (videoSystem != null) {
            videoSystem.release();
            videoSystem = null;
            Log.d(TAG, "TpVideoSystem预览已停止");
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startCameraPreview();
            } else {
                Toast.makeText(this, "需要相机权限才能显示实时预览", Toast.LENGTH_LONG).show();
                tvPreviewInfo.setText("无相机权限");
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // 如果视频系统已经初始化，重新启动
        if (videoSystem != null && ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            }
        } else if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        stopCameraPreview();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopCameraPreview();
        Log.d(TAG, "Activity已销毁，资源已清理");
    }
}
