[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_dialog_smb_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\dialog_smb_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_media_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\media_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_dialog_image_format_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\dialog_image_format_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_video_play_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_video_play_button_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-37:/layout_activity_image_video_compare.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-39:/layout/activity_image_video_compare.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_image_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\image_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_pause_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_pause_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_video_settings_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_video_settings_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_tp_speed_selection_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\tp_speed_selection_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\color_tp_video_button_text_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\color\\tp_video_button_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_media_browser.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\media_browser.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_fast_rewind_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_fast_rewind_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_fragment_tp_network_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\fragment_tp_network_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_item_settings_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\item_settings_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_settings_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_settings_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_tp_video_player_controls.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\tp_video_player_controls.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_play_arrow_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_play_arrow_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_dialog_tp_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\dialog_tp_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_video_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_video_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\anim_tp_speed_dropdown_enter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\anim\\tp_speed_dropdown_enter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_fast_forward_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_fast_forward_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\anim_tp_speed_dialog_exit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\anim\\tp_speed_dialog_exit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_encoder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\encoder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_spinner_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\spinner_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_video_progress_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_video_progress_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_video_controls_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_video_controls_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_skip_next_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_skip_next_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_decoder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\decoder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_video_progress_drawable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_video_progress_drawable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\animator_tp_video_button_release.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\animator\\tp_video_button_release.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_activity_tp_video_player_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\activity_tp_video_player_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_network_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\network_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_skip_previous_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_skip_previous_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_speed_dropdown_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_speed_dropdown_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_fragment_tp_smb_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\fragment_tp_smb_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_speed_menu_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_speed_menu_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_fragment_tp_tv_mode_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\fragment_tp_tv_mode_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_activity_image_video_compare.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\activity_image_video_compare.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_ic_step_frame_white_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\ic_step_frame_white_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_speed_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_speed_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\drawable_tp_speed_item_selected_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\drawable\\tp_speed_item_selected_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\animator_tp_video_button_press.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\animator\\tp_video_button_press.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_tp_speed_dropdown_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\tp_speed_dropdown_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_popup_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\popup_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_dialog_tp_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\dialog_tp_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\anim_tp_speed_dialog_enter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\anim\\tp_speed_dialog_enter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\layout_media_browser_integrated.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\layout\\media_browser_integrated.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-debug-37:\\anim_tp_speed_dropdown_exit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.android.rockchip.mediacodecnew.app-main-39:\\anim\\tp_speed_dropdown_exit.xml"}]